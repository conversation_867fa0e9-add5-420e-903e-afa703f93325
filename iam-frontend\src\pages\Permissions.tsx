import React, { useState, useEffect } from 'react'
import { useAuth } from '../hooks/useAuth'
import { apiService } from '../services/api'

import Modal from '../components/Modal'
import LoadingSpinner from '../components/LoadingSpinner'

const Permissions: React.FC = () => {
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [modules, setModules] = useState<Module[]>([])
  const [roles, setRoles] = useState<Role[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isRoleModalOpen, setIsRoleModalOpen] = useState(false)
  const [editingPermission, setEditingPermission] = useState<Permission | null>(null)
  const [selectedPermission, setSelectedPermission] = useState<Permission | null>(null)
  const [formData, setFormData] = useState<CreatePermissionRequest>({
    action: '',
    moduleId: ''
  })
  const [formErrors, setFormErrors] = useState<FormErrors>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedRoleIds, setSelectedRoleIds] = useState<string[]>([])

  const { hasPermission } = useAuth()

  const canCreate = hasPermission('Permissions', 'create')
  const canUpdate = hasPermission('Permissions', 'update')
  const canDelete = hasPermission('Permissions', 'delete')

  const commonActions = ['create', 'read', 'update', 'delete']

  useEffect(() => {
    fetchPermissions()
    fetchModules()
    fetchRoles()
  }, [])

  const fetchPermissions = async () => {
    try {
      setLoading(true)
      const response = await apiService.getPermissions()
      console.log(response.data)
      if (response.success && response.data && response.data.permissions) {
        setPermissions(response.data.permissions)
      } else {
        setError(response.message || 'Failed to fetch permissions')
      }
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error && 'response' in err
          ? (err as { response?: { data?: { message?: string } } }).response?.data?.message
          : 'Failed to fetch permissions'
      setError(errorMessage || 'Failed to fetch permissions')
    } finally {
      setLoading(false)
    }
  }

  const fetchModules = async () => {
    try {
      const response = await apiService.getModules()
      if (response.success && response.data) {
        setModules(response.data)
      }
    } catch (err: unknown) {
      console.error('Failed to fetch modules:', err)
    }
  }

  const fetchRoles = async () => {
    try {
      const response = await apiService.getRoles()
      if (response.success && response.data) {
        setRoles(response.data)
      }
    } catch (err: unknown) {
      console.error('Failed to fetch roles:', err)
    }
  }

  const handleOpenModal = (permission?: Permission) => {
    if (permission) {
      setEditingPermission(permission)
      setFormData({
        action: permission.action,
        moduleId: permission.moduleId
      })
    } else {
      setEditingPermission(null)
      setFormData({
        action: '',
        moduleId: ''
      })
    }
    setFormErrors({})
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setEditingPermission(null)
    setFormData({
      action: '',
      moduleId: ''
    })
    setFormErrors({})
  }

  const handleOpenRoleModal = (permission: Permission) => {
    setSelectedPermission(permission)
    // Pre-select roles that already have this permission
    const permissionRoleIds = permission.roles?.map((role: Role) => role.id) || []
    setSelectedRoleIds(permissionRoleIds)
    setIsRoleModalOpen(true)
  }

  const handleCloseRoleModal = () => {
    setIsRoleModalOpen(false)
    setSelectedPermission(null)
    setSelectedRoleIds([])
  }

  const validateForm = (): boolean => {
    const errors: FormErrors = {}

    if (!formData.action.trim()) {
      errors.action = 'Action is required'
    }

    if (!formData.moduleId) {
      errors.moduleId = 'Module is required'
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))

    // Clear error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    try {
      if (editingPermission) {
        // Update permission
        const response = await apiService.updatePermission(editingPermission.id, formData)
        if (response.success) {
          await fetchPermissions()
          handleCloseModal()
        } else {
          setError(response.message || 'Failed to update permission')
        }
      } else {
        // Create permission
        const response = await apiService.createPermission(formData)
        if (response.success) {
          await fetchPermissions()
          handleCloseModal()
        } else {
          setError(response.message || 'Failed to create permission')
        }
      }
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error && 'response' in err
          ? (err as { response?: { data?: { message?: string } } }).response?.data?.message
          : 'Failed to save permission'
      setError(errorMessage || 'Failed to save permission')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDelete = async (permission: Permission) => {
    if (
      !window.confirm(
        `Are you sure you want to delete permission "${permission.action}" on "${permission.module?.name}"?`
      )
    ) {
      return
    }

    try {
      const response = await apiService.deletePermission(permission.id)
      if (response.success) {
        await fetchPermissions()
      } else {
        setError(response.message || 'Failed to delete permission')
      }
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error && 'response' in err
          ? (err as { response?: { data?: { message?: string } } }).response?.data?.message
          : 'Failed to delete permission'
      setError(errorMessage || 'Failed to delete permission')
    }
  }

  const handleRoleSelection = (roleId: string) => {
    setSelectedRoleIds(prev => {
      if (prev.includes(roleId)) {
        return prev.filter(id => id !== roleId)
      } else {
        return [...prev, roleId]
      }
    })
  }

  const handleAssignPermissionsToRoles = async () => {
    if (!selectedPermission) return

    setIsSubmitting(true)
    try {
      // For each selected role, assign this permission
      for (const roleId of selectedRoleIds) {
        await apiService.assignPermissionsToRole(roleId, {
          permissionIds: [selectedPermission.id]
        })
      }

      // Remove permission from roles that are no longer selected
      const currentRoleIds = selectedPermission.roles?.map((r: Role) => r.id) || []
      const rolesToRemoveFrom = currentRoleIds.filter((id: string) => !selectedRoleIds.includes(id))

      for (const roleId of rolesToRemoveFrom) {
        await apiService.removePermissionFromRole(roleId, selectedPermission.id)
      }

      await fetchPermissions()
      handleCloseRoleModal()
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error && 'response' in err
          ? (err as { response?: { data?: { message?: string } } }).response?.data?.message
          : 'Failed to assign permissions to roles'
      setError(errorMessage || 'Failed to assign permissions to roles')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleRemovePermissionFromRole = async (roleId: string, permissionId: string) => {
    if (!window.confirm('Are you sure you want to remove this permission from the role?')) {
      return
    }

    try {
      const response = await apiService.removePermissionFromRole(roleId, permissionId)
      if (response.success) {
        await fetchPermissions()
      } else {
        setError(response.message || 'Failed to remove permission from role')
      }
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error && 'response' in err
          ? (err as { response?: { data?: { message?: string } } }).response?.data?.message
          : 'Failed to remove permission from role'
      setError(errorMessage || 'Failed to remove permission from role')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Permissions Management</h1>
        {canCreate && (
          <button onClick={() => handleOpenModal()} className="btn-primary">
            Add Permission
          </button>
        )}
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-sm text-red-700">{error}</div>
          <button onClick={() => setError('')} className="mt-2 text-sm text-red-600 hover:text-red-800">
            Dismiss
          </button>
        </div>
      )}

      {/* Permissions table */}
      <div className="card p-0">
        <div className="table-container">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th className="table-header-cell">Action</th>
                <th className="table-header-cell">Module</th>
                <th className="table-header-cell">Roles</th>
                <th className="table-header-cell">Created</th>
                <th className="table-header-cell">Actions</th>
              </tr>
            </thead>
            <tbody className="table-body">
              {permissions.map((permission: Permission) => (
                <tr key={permission.id}>
                  <td className="table-cell">
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-orange-600 rounded-full flex items-center justify-center mr-3">
                        <span className="text-white text-sm font-medium">{permission.action[0].toUpperCase()}</span>
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900 capitalize">{permission.action}</div>
                      </div>
                    </div>
                  </td>
                  <td className="table-cell">
                    <div className="flex items-center">
                      <div className="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center mr-2">
                        <span className="text-white text-xs font-medium">
                          {permission.module?.name[0].toUpperCase()}
                        </span>
                      </div>
                      <span className="text-sm text-gray-900">{permission.module?.name}</span>
                    </div>
                  </td>
                  <td className="table-cell">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-900">{permission.roles?.length || 0} roles</span>
                      {permission.roles && permission.roles.length > 0 && (
                        <div className="flex -space-x-1">
                          {permission.roles.slice(0, 3).map((role: Role) => (
                            <div
                              key={role.id}
                              className="w-6 h-6 bg-indigo-500 rounded-full flex items-center justify-center border-2 border-white"
                              title={role.name}
                            >
                              <span className="text-white text-xs font-medium">{role.name[0].toUpperCase()}</span>
                            </div>
                          ))}
                          {permission.roles.length > 3 && (
                            <div className="w-6 h-6 bg-gray-500 rounded-full flex items-center justify-center border-2 border-white">
                              <span className="text-white text-xs font-medium">+{permission.roles.length - 3}</span>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="table-cell">{new Date(permission.createdAt).toLocaleDateString()}</td>
                  <td className="table-cell">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleOpenRoleModal(permission)}
                        className="text-green-600 hover:text-green-900 text-sm"
                      >
                        Manage Roles
                      </button>
                      {canUpdate && (
                        <button
                          onClick={() => handleOpenModal(permission)}
                          className="text-blue-600 hover:text-blue-900 text-sm"
                        >
                          Edit
                        </button>
                      )}
                      {canDelete && (
                        <button
                          onClick={() => handleDelete(permission)}
                          className="text-red-600 hover:text-red-900 text-sm"
                        >
                          Delete
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {permissions.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-500">No permissions found.</p>
            </div>
          )}
        </div>
      </div>

      {/* Permission form modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        title={editingPermission ? 'Edit Permission' : 'Add Permission'}
        size="md"
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="moduleId" className="form-label">
              Module
            </label>
            <select
              id="moduleId"
              name="moduleId"
              className={`form-input ${
                formErrors.moduleId ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''
              }`}
              value={formData.moduleId}
              onChange={handleInputChange}
              required
            >
              <option value="">Select a module</option>
              {modules.map((module: Module) => (
                <option key={module.id} value={module.id}>
                  {module.name}
                </option>
              ))}
            </select>
            {formErrors.moduleId && <p className="mt-1 text-sm text-red-600">{formErrors.moduleId}</p>}
          </div>

          <div>
            <label htmlFor="action" className="form-label">
              Action
            </label>
            <select
              id="action"
              name="action"
              className={`form-input ${
                formErrors.action ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''
              }`}
              value={formData.action}
              onChange={handleInputChange}
              required
            >
              <option value="">Select an action</option>
              {commonActions.map((action: string) => (
                <option key={action} value={action}>
                  {action.charAt(0).toUpperCase() + action.slice(1)}
                </option>
              ))}
            </select>
            {formErrors.action && <p className="mt-1 text-sm text-red-600">{formErrors.action}</p>}
            <p className="mt-1 text-xs text-gray-500">Common actions: Create, Read, Update, Delete</p>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button type="button" onClick={handleCloseModal} className="btn-secondary">
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <>
                  <LoadingSpinner size="sm" className="mr-2" />
                  {editingPermission ? 'Updating...' : 'Creating...'}
                </>
              ) : editingPermission ? (
                'Update Permission'
              ) : (
                'Create Permission'
              )}
            </button>
          </div>
        </form>
      </Modal>

      {/* Role assignment modal */}
      <Modal
        isOpen={isRoleModalOpen}
        onClose={handleCloseRoleModal}
        title={`Manage Roles - ${selectedPermission?.action} on ${selectedPermission?.module?.name}`}
        size="lg"
      >
        <div className="space-y-4">
          <p className="text-sm text-gray-600">
            Select roles to assign this permission to. Roles that already have this permission are pre-selected.
          </p>

          {/* Current permission assignments */}
          {selectedPermission?.roles && selectedPermission.roles.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-2">Current Assignments</h4>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {selectedPermission.roles.map((role: Role) => (
                  <div key={role.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <div className="flex items-center">
                      <div className="w-6 h-6 bg-indigo-600 rounded-full flex items-center justify-center mr-2">
                        <span className="text-white text-xs font-medium">{role.name[0].toUpperCase()}</span>
                      </div>
                      <span className="text-sm text-gray-900">{role.name}</span>
                    </div>
                    <button
                      onClick={() => handleRemovePermissionFromRole(role.id, selectedPermission.id)}
                      className="text-red-600 hover:text-red-800 text-xs"
                    >
                      Remove
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Available roles */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">Available Roles</h4>
            <div className="space-y-2 max-h-64 overflow-y-auto border border-gray-200 rounded p-2">
              {roles.map((role: Role) => (
                <label key={role.id} className="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedRoleIds.includes(role.id)}
                    onChange={() => handleRoleSelection(role.id)}
                    className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <div className="flex items-center">
                    <div className="w-6 h-6 bg-indigo-600 rounded-full flex items-center justify-center mr-2">
                      <span className="text-white text-xs font-medium">{role.name[0].toUpperCase()}</span>
                    </div>
                    <div>
                      <div className="text-sm font-medium text-gray-900">{role.name}</div>
                      <div className="text-xs text-gray-500">{role.description}</div>
                    </div>
                  </div>
                </label>
              ))}
            </div>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button type="button" onClick={handleCloseRoleModal} className="btn-secondary">
              Cancel
            </button>
            <button
              onClick={handleAssignPermissionsToRoles}
              disabled={isSubmitting}
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <>
                  <LoadingSpinner size="sm" className="mr-2" />
                  Updating...
                </>
              ) : (
                'Update Permission Assignments'
              )}
            </button>
          </div>
        </div>
      </Modal>
    </div>
  )
}

export default Permissions
