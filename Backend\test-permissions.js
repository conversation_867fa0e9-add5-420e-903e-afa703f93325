import { PrismaClient } from '@prisma/client'
import { getUserPermissions } from './src/utils/permissionService.js'

const prisma = new PrismaClient()

async function testPermissions() {
  try {
    console.log('🧪 Testing permission system...')
    
    // Find the admin user
    const adminUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })
    
    if (!adminUser) {
      console.log('❌ Admin user not found')
      return
    }
    
    console.log('✅ Found admin user:', adminUser.email)
    
    // Get user permissions
    const permissions = await getUserPermissions(adminUser.id)
    console.log('📋 User permissions:', permissions.length)
    
    // Check for Permissions module read permission
    const hasPermissionsRead = permissions.some(
      p => p.module.name === 'Permissions' && p.action === 'read'
    )
    
    console.log('🔍 Has Permissions read permission:', hasPermissionsRead)
    
    // List all permissions for debugging
    console.log('\n📝 All permissions:')
    permissions.forEach(p => {
      console.log(`  - ${p.module.name}: ${p.action} (from ${p.inheritedFrom.group} -> ${p.inheritedFrom.role})`)
    })
    
    // Transform permissions to frontend format
    const transformedPermissions = permissions.map(permission => ({
      module: permission.module.name,
      action: permission.action
    }))
    
    console.log('\n🔄 Transformed permissions for frontend:')
    transformedPermissions.forEach(p => {
      console.log(`  - ${p.module}: ${p.action}`)
    })
    
  } catch (error) {
    console.error('❌ Error testing permissions:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testPermissions()
