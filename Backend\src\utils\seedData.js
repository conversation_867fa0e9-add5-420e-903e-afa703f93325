import { prisma } from '../config/database.js'
import { hashPassword } from './password.js'

/**
 * Seed the database with sample data for testing
 */
const seedDatabase = async () => {
  try {
    console.log('🌱 Starting database seeding...')

    // Create sample modules
    const modules = await Promise.all([
      prisma.module.upsert({
        where: { name: 'Users' },
        update: {},
        create: {
          name: 'Users',
          description: 'User management and administration'
        }
      }),
      prisma.module.upsert({
        where: { name: 'Groups' },
        update: {},
        create: {
          name: 'Groups',
          description: 'Group management and organization'
        }
      }),
      prisma.module.upsert({
        where: { name: 'Roles' },
        update: {},
        create: {
          name: 'Roles',
          description: 'Role definition and management'
        }
      }),
      prisma.module.upsert({
        where: { name: 'Reports' },
        update: {},
        create: {
          name: 'Reports',
          description: 'System reports and analytics'
        }
      }),
      prisma.module.upsert({
        where: { name: 'Modu<PERSON>' },
        update: {},
        create: {
          name: 'Modu<PERSON>',
          description: 'Module definition and management'
        }
      }),
      prisma.module.upsert({
        where: { name: 'Permissions' },
        update: {},
        create: {
          name: 'Permissions',
          description: 'Permission definition and management'
        }
      })
    ])

    console.log('✅ Created modules:', modules.map(m => m.name).join(', '))

    // Create permissions for each module
    const actions = ['create', 'read', 'update', 'delete']
    const permissions = []

    for (const module of modules) {
      for (const action of actions) {
        const permission = await prisma.permission.upsert({
          where: {
            action_moduleId: {
              action,
              moduleId: module.id
            }
          },
          update: {},
          create: {
            action,
            moduleId: module.id,
            description: `${action.charAt(0).toUpperCase() + action.slice(1)} ${module.name.toLowerCase()}`
          }
        })
        permissions.push(permission)
      }
    }

    console.log('✅ Created permissions:', permissions.length)

    // Create sample roles
    const adminRole = await prisma.role.upsert({
      where: { name: 'Administrator' },
      update: {},
      create: {
        name: 'Administrator',
        description: 'Full system access'
      }
    })

    const managerRole = await prisma.role.upsert({
      where: { name: 'Manager' },
      update: {},
      create: {
        name: 'Manager',
        description: 'Management level access'
      }
    })

    const userRole = await prisma.role.upsert({
      where: { name: 'User' },
      update: {},
      create: {
        name: 'User',
        description: 'Basic user access'
      }
    })

    console.log('✅ Created roles: Administrator, Manager, User')

    // Assign all permissions to Administrator role
    for (const permission of permissions) {
      await prisma.rolePermission.upsert({
        where: {
          roleId_permissionId: {
            roleId: adminRole.id,
            permissionId: permission.id
          }
        },
        update: {},
        create: {
          roleId: adminRole.id,
          permissionId: permission.id,
          assignedBy: 'system'
        }
      })
    }

    // Assign read permissions to User role
    const readPermissions = permissions.filter(p => p.action === 'read')
    for (const permission of readPermissions) {
      await prisma.rolePermission.upsert({
        where: {
          roleId_permissionId: {
            roleId: userRole.id,
            permissionId: permission.id
          }
        },
        update: {},
        create: {
          roleId: userRole.id,
          permissionId: permission.id,
          assignedBy: 'system'
        }
      })
    }

    console.log('✅ Assigned permissions to roles')

    // Create sample groups
    const adminGroup = await prisma.group.upsert({
      where: { name: 'Administrators' },
      update: {},
      create: {
        name: 'Administrators',
        description: 'System administrators'
      }
    })

    const hrGroup = await prisma.group.upsert({
      where: { name: 'HR Department' },
      update: {},
      create: {
        name: 'HR Department',
        description: 'Human Resources team'
      }
    })

    const employeeGroup = await prisma.group.upsert({
      where: { name: 'Employees' },
      update: {},
      create: {
        name: 'Employees',
        description: 'General employees'
      }
    })

    console.log('✅ Created groups: Administrators, HR Department, Employees')

    // Assign roles to groups
    await prisma.groupRole.upsert({
      where: {
        groupId_roleId: {
          groupId: adminGroup.id,
          roleId: adminRole.id
        }
      },
      update: {},
      create: {
        groupId: adminGroup.id,
        roleId: adminRole.id,
        assignedBy: 'system'
      }
    })

    await prisma.groupRole.upsert({
      where: {
        groupId_roleId: {
          groupId: hrGroup.id,
          roleId: managerRole.id
        }
      },
      update: {},
      create: {
        groupId: hrGroup.id,
        roleId: managerRole.id,
        assignedBy: 'system'
      }
    })

    await prisma.groupRole.upsert({
      where: {
        groupId_roleId: {
          groupId: employeeGroup.id,
          roleId: userRole.id
        }
      },
      update: {},
      create: {
        groupId: employeeGroup.id,
        roleId: userRole.id,
        assignedBy: 'system'
      }
    })

    console.log('✅ Assigned roles to groups')

    // Create sample users
    const hashedPassword = await hashPassword('Password123!')

    const adminUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        username: 'admin',
        password: hashedPassword,
        firstName: 'System',
        lastName: 'Administrator'
      }
    })

    const hrUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        username: 'hrmanager',
        password: hashedPassword,
        firstName: 'HR',
        lastName: 'Manager'
      }
    })

    const regularUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        username: 'employee',
        password: hashedPassword,
        firstName: 'Regular',
        lastName: 'Employee'
      }
    })

    console.log('✅ Created users: admin, hrmanager, employee')

    // Assign users to groups
    await prisma.userGroup.upsert({
      where: {
        userId_groupId: {
          userId: adminUser.id,
          groupId: adminGroup.id
        }
      },
      update: {},
      create: {
        userId: adminUser.id,
        groupId: adminGroup.id,
        assignedBy: 'system'
      }
    })

    await prisma.userGroup.upsert({
      where: {
        userId_groupId: {
          userId: hrUser.id,
          groupId: hrGroup.id
        }
      },
      update: {},
      create: {
        userId: hrUser.id,
        groupId: hrGroup.id,
        assignedBy: 'system'
      }
    })

    await prisma.userGroup.upsert({
      where: {
        userId_groupId: {
          userId: regularUser.id,
          groupId: employeeGroup.id
        }
      },
      update: {},
      create: {
        userId: regularUser.id,
        groupId: employeeGroup.id,
        assignedBy: 'system'
      }
    })

    console.log('✅ Assigned users to groups')

    console.log('🎉 Database seeding completed successfully!')
    console.log('\n📋 Sample accounts created:')
    console.log('  Admin: <EMAIL> / Password123!')
    console.log('  HR Manager: <EMAIL> / Password123!')
    console.log('  Employee: <EMAIL> / Password123!')
  } catch (error) {
    console.error('❌ Error seeding database:', error)
    throw error
  }
}

// Run seeder if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  seedDatabase()
    .then(() => {
      console.log('Seeding completed')
      process.exit(0)
    })
    .catch(error => {
      console.error('Seeding failed:', error)
      process.exit(1)
    })
}

export { seedDatabase }
